package controller

import (
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/svc"
	"gitee.com/rcztcs/zjl/internal/zjl/types"
	"github.com/gin-gonic/gin"
)

// registerRiskRoutes 注册风险相关的路由
func registerRiskRoutes(r *gin.RouterGroup, svc *svc.ServiceContext) {
	risks := r.Group("/risks")
	{
		// 风险资源管理
		risks.POST("", CreateRisk(svc))                        // POST /risks - 创建风险
		risks.GET("", ListRisks(svc))                          // GET /risks - 获取风险列表（支持多种筛选条件）
		risks.GET("/:id", GetRiskByID(svc))                    // GET /risks/:id - 获取指定风险详情
		risks.PUT("/:id", UpdateRisk(svc))                     // PUT /risks/:id - 更新风险信息
		risks.DELETE("/:id", DeleteRisk(svc))                  // DELETE /risks/:id - 删除风险
		risks.GET("/task/:task_id", GetRisksByTaskID(svc))     // GET /risks/task/:task_id - 根据任务ID获取风险列表
	}
}

// CreateRisk 创建风险
func CreateRisk(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.CreateRiskRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 调用服务层创建风险
		risk, err := svc.RiskService.CreateRisk(req.TaskID, req.RiskLevel, req.IndicatorType, req.IndicatorName)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseSuccess(c, types.CreateRiskResponse{Risk: risk})
	}
}

// GetRiskByID 获取风险详情
func GetRiskByID(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.GetRiskRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 调用服务层获取风险详情
		risk, err := svc.RiskService.GetRiskByID(req.ID)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseSuccess(c, types.GetRiskResponse{Risk: risk})
	}
}

// GetRisksByTaskID 根据任务ID获取风险列表
func GetRisksByTaskID(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.GetRisksByTaskIDRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 调用服务层获取风险列表
		risks, err := svc.RiskService.GetRisksByTaskID(req.TaskID)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseSuccess(c, types.GetRisksByTaskIDResponse{Risks: risks})
	}
}

// ListRisks 获取风险列表（支持多种筛选条件）
func ListRisks(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.ListRisksRequest

		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		risks, total, err := svc.RiskService.ListRisks(req.Page, req.PageSize, req.TaskID, req.RiskLevel, req.IndicatorType)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseList(c, risks, total)
	}
}

// UpdateRisk 更新风险信息
func UpdateRisk(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.UpdateRiskRequest
		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 调用服务层更新风险信息
		err = svc.RiskService.UpdateRisk(req.ID, req.RiskLevel, req.IndicatorType, req.IndicatorName)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseSuccess(c, nil)
	}
}

// DeleteRisk 删除风险
func DeleteRisk(svc *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.DeleteRiskRequest
		err := utils.ParseRequest(c, &req)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		// 调用服务层删除风险
		err = svc.RiskService.DeleteRisk(req.ID)
		if err != nil {
			utils.ResponseError(c, err)
			return
		}

		utils.ResponseSuccess(c, nil)
	}
}
